"""
Comprehensive example for handling SelectorGroupChat events with enhanced MessageProcessor.
Demonstrates speaker selection, selector model responses, and selection failures.
"""

import asyncio
import json
from typing import Any, Dict, List, Optional

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.base import TaskResult
from autogen_agentchat.conditions import MaxMessageTermination, TextMentionTermination
from autogen_agentchat.teams import SelectorGroupChat
from autogen_ext.models.openai import OpenAIChatCompletionClient

from ..autogen_service.message_processor import MessageProcessor


class SelectorGroupChatEventHandler:
    """
    Comprehensive handler for SelectorGroupChat events including
    speaker selection, selector model responses, and selection failures.
    """

    def __init__(self, model_client: OpenAIChatCompletionClient):
        self.model_client = model_client
        self.message_processor = MessageProcessor()
        self.conversation_log: List[Dict[str, Any]] = []
        self.selector_stats = {
            "total_selections": 0,
            "successful_selections": 0,
            "failed_selections": 0,
            "repeated_speakers": 0,
            "selector_model_calls": 0,
            "speaker_distribution": {},
            "selection_reasons": [],
        }

    async def create_research_team(self) -> SelectorGroupChat:
        """Create a research team with SelectorGroupChat"""
        
        # Define tools for demonstration
        async def search_web(query: str) -> str:
            """Search the web for information"""
            await asyncio.sleep(0.1)
            return f"Web search results for '{query}': Found relevant articles and data"

        async def analyze_data(data: str) -> str:
            """Analyze provided data"""
            await asyncio.sleep(0.1)
            return f"Data analysis of '{data}': Key insights and trends identified"

        async def generate_report(content: str) -> str:
            """Generate a comprehensive report"""
            await asyncio.sleep(0.1)
            return f"Generated report based on: {content[:50]}..."

        # Create specialized agents
        coordinator = AssistantAgent(
            name="coordinator",
            model_client=self.model_client,
            system_message=(
                "You are a research coordinator. Break down research tasks and "
                "assign them to appropriate team members. Mention specific agent "
                "names when delegating tasks. End with 'RESEARCH_COMPLETE' when done."
            ),
            description="Coordinates research projects and delegates tasks",
        )

        web_researcher = AssistantAgent(
            name="web_researcher",
            model_client=self.model_client,
            tools=[search_web],
            system_message=(
                "You are a web research specialist. Use your search tool to "
                "find relevant information on assigned topics."
            ),
            description="Specializes in web research and information gathering",
        )

        data_analyst = AssistantAgent(
            name="data_analyst",
            model_client=self.model_client,
            tools=[analyze_data],
            system_message=(
                "You are a data analyst. Analyze data provided by other team "
                "members and extract meaningful insights."
            ),
            description="Analyzes data and extracts insights",
        )

        report_writer = AssistantAgent(
            name="report_writer",
            model_client=self.model_client,
            tools=[generate_report],
            system_message=(
                "You are a report writer. Create comprehensive reports based "
                "on research and analysis from team members."
            ),
            description="Creates comprehensive reports and documentation",
        )

        # Custom selector function with detailed logging
        def advanced_selector_func(messages) -> Optional[str]:
            """Advanced selector function with detailed selection logic"""
            if not messages:
                return coordinator.name
            
            last_message = messages[-1]
            last_speaker = last_message.source
            
            # Selection logic based on content and context
            content = last_message.content.lower()
            
            # If coordinator is delegating, select appropriate specialist
            if last_speaker == coordinator.name:
                if "search" in content or "web" in content or "find" in content:
                    return web_researcher.name
                elif "analyze" in content or "data" in content:
                    return data_analyst.name
                elif "report" in content or "write" in content:
                    return report_writer.name
            
            # If specialist completed task, return to coordinator
            elif last_speaker in [web_researcher.name, data_analyst.name, report_writer.name]:
                if "completed" in content or "finished" in content or "done" in content:
                    return coordinator.name
            
            # Default fallback
            return coordinator.name

        # Create termination conditions
        termination = (
            TextMentionTermination("RESEARCH_COMPLETE") | 
            MaxMessageTermination(25)
        )

        # Create SelectorGroupChat
        team = SelectorGroupChat(
            participants=[coordinator, web_researcher, data_analyst, report_writer],
            model_client=self.model_client,
            termination_condition=termination,
            selector_func=advanced_selector_func,
            allow_repeated_speaker=False,  # Prevent immediate repetition
        )

        return team

    async def process_selector_message(self, message: Any) -> Dict[str, Any]:
        """Process SelectorGroupChat message with comprehensive details"""
        processed = self.message_processor.process_stream_message(message)
        
        # Update selector statistics
        self._update_selector_stats(processed, message)
        
        # Add to conversation log
        self.conversation_log.append(processed)
        
        return processed

    def _update_selector_stats(self, processed: Dict[str, Any], original_message: Any):
        """Update SelectorGroupChat statistics"""
        
        # Track speaker distribution
        source = processed.get("source", "unknown")
        if source not in self.selector_stats["speaker_distribution"]:
            self.selector_stats["speaker_distribution"][source] = 0
        self.selector_stats["speaker_distribution"][source] += 1
        
        # Track selector-specific events
        selector_event_type = processed.get("metadata", {}).get("selector_event_type")
        if selector_event_type:
            if selector_event_type == "speaker_selection":
                self.selector_stats["total_selections"] += 1
                self.selector_stats["successful_selections"] += 1
                
                # Track selection reason if available
                selector_details = processed.get("metadata", {}).get("selector_details", {})
                if "selection_reason" in selector_details:
                    self.selector_stats["selection_reasons"].append(
                        selector_details["selection_reason"]
                    )
            
            elif selector_event_type == "selection_failure":
                self.selector_stats["total_selections"] += 1
                self.selector_stats["failed_selections"] += 1
            
            elif selector_event_type == "repeated_speaker":
                self.selector_stats["repeated_speakers"] += 1
            
            elif selector_event_type == "selector_model_response":
                self.selector_stats["selector_model_calls"] += 1

    async def run_selector_group_chat_demo(self, task: str) -> List[Dict[str, Any]]:
        """Run SelectorGroupChat demonstration with comprehensive event handling"""
        print("🎯 Starting SelectorGroupChat Demo with Advanced Event Handling")
        print("=" * 70)
        
        team = await self.create_research_team()
        processed_messages = []
        
        try:
            async for message in team.run_stream(task=task):
                processed = await self.process_selector_message(message)
                processed_messages.append(processed)
                
                self._print_selector_message(processed)
                
                # Handle specific selector events
                if isinstance(message, TaskResult):
                    print(f"\n✅ SelectorGroupChat completed: {message.stop_reason}")
                    break
                    
        except Exception as e:
            print(f"❌ Error in SelectorGroupChat: {e}")
        
        return processed_messages

    def _print_selector_message(self, processed: Dict[str, Any]):
        """Print formatted SelectorGroupChat message with selector details"""
        msg_type = processed["type"]
        source = processed["source"]
        content = processed["content"]
        
        # Icons for different message types
        icons = {
            "text": "💬",
            "tool_call_request": "🔧",
            "tool_call_execution": "⚙️",
            "response": "📋",
            "task_result": "🎯",
        }
        
        icon = icons.get(msg_type, "📝")
        print(f"{icon} [{source}] {content}")
        
        # Print selector-specific details
        selector_details = processed.get("metadata", {}).get("selector_details", {})
        if selector_details:
            print("   🎯 Selector Details:")
            for key, value in selector_details.items():
                if key == "speaker_history" and isinstance(value, list):
                    print(f"      {key}: {value[-3:] if len(value) > 3 else value}")  # Last 3
                elif key == "selection_context":
                    print(f"      {key}: {value} messages")
                else:
                    print(f"      {key}: {value}")
        
        # Print selector event type
        selector_event_type = processed.get("metadata", {}).get("selector_event_type")
        if selector_event_type:
            print(f"   🔍 Selector Event: {selector_event_type}")
        
        # Print group chat details
        group_details = processed.get("metadata", {}).get("group_chat_details", {})
        if group_details and "next_speaker" in group_details:
            print(f"   → Next speaker: {group_details['next_speaker']}")

    def get_selector_summary(self) -> Dict[str, Any]:
        """Get comprehensive SelectorGroupChat summary"""
        total_messages = len(self.conversation_log)
        
        return {
            "conversation_length": total_messages,
            "selector_statistics": self.selector_stats,
            "selection_success_rate": (
                self.selector_stats["successful_selections"] / 
                max(self.selector_stats["total_selections"], 1)
            ) * 100,
            "average_messages_per_speaker": (
                total_messages / 
                max(len(self.selector_stats["speaker_distribution"]), 1)
            ),
            "most_active_speaker": max(
                self.selector_stats["speaker_distribution"].items(),
                key=lambda x: x[1],
                default=("none", 0)
            )[0],
        }

    async def simulate_selector_events(self):
        """Simulate various selector events for testing"""
        print("\n🧪 Simulating SelectorGroupChat Events")
        print("=" * 50)
        
        # Simulate speaker selection event
        mock_selection_message = type('MockMessage', (), {
            'source': 'coordinator',
            'content': 'Delegating web research task',
            'selector_result': 'web_researcher',
            'selection_reason': 'Content mentions web research',
            'available_speakers': ['coordinator', 'web_researcher', 'data_analyst'],
            'allow_repeated_speaker': False,
            'speaker_history': ['coordinator', 'web_researcher', 'coordinator'],
        })()
        
        processed = self.message_processor.process_stream_message(mock_selection_message)
        print("📋 Simulated Speaker Selection Event:")
        print(json.dumps(processed, indent=2))
        
        # Simulate selection failure event
        mock_failure_message = type('MockMessage', (), {
            'source': 'system',
            'content': 'Speaker selection failed',
            'selector_result': None,
            'selection_error': 'No suitable speaker found',
        })()
        
        processed = self.message_processor.process_stream_message(mock_failure_message)
        print("\n❌ Simulated Selection Failure Event:")
        print(json.dumps(processed, indent=2))

    async def close(self):
        """Clean up resources"""
        await self.model_client.close()


# Example usage
async def main():
    """Demonstrate comprehensive SelectorGroupChat event handling"""
    model_client = OpenAIChatCompletionClient(
        model="gpt-4o",
        # api_key="your-api-key-here"
    )
    
    handler = SelectorGroupChatEventHandler(model_client)
    
    # Complex research task that will trigger multiple selector events
    research_task = (
        "Conduct a comprehensive research project on the impact of AI on "
        "modern healthcare. Search for recent developments, analyze the data, "
        "and create a detailed report with recommendations."
    )
    
    try:
        # Run the demonstration
        messages = await handler.run_selector_group_chat_demo(research_task)
        
        # Simulate additional events
        await handler.simulate_selector_events()
        
        # Print comprehensive summary
        print("\n" + "=" * 70)
        print("📊 COMPREHENSIVE SELECTOR GROUP CHAT SUMMARY")
        print("=" * 70)
        
        summary = handler.get_selector_summary()
        print(json.dumps(summary, indent=2))
        
        # Print key insights
        print(f"\n🎯 Key Insights:")
        print(f"   • Total messages: {summary['conversation_length']}")
        print(f"   • Selection success rate: {summary['selection_success_rate']:.1f}%")
        print(f"   • Most active speaker: {summary['most_active_speaker']}")
        print(f"   • Avg messages per speaker: {summary['average_messages_per_speaker']:.1f}")
        
    finally:
        await handler.close()


if __name__ == "__main__":
    asyncio.run(main())
